<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class UserManagementController extends Controller
{
    /**
     * Display a listing of all users
     */
    public function index()
    {
        $users = User::orderByRaw("
            CASE role
                WHEN 'admin' THEN 1
                WHEN 'member' THEN 2
                WHEN 'user' THEN 3
                ELSE 4
            END
        ")
        ->orderBy('name')
        ->orderBy('username')
        ->paginate(15);

        return view('admin.users.index', compact('users'));
    }

    /**
     * Show the form for creating a new user
     */
    public function create()
    {
        return view('admin.users.create');
    }

    /**
     * Store a newly created user in storage
     */
    public function store(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'username' => ['nullable', 'string', 'max:255', 'alpha_dash', 'unique:users'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'role' => ['required', Rule::in(['admin', 'member', 'user'])],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Create the user
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'username' => $request->username,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'email_verified_at' => now(), // Auto-verify admin-created users
        ]);

        return redirect()->route('admin.users.show', $user)
            ->with('success', 'User created successfully.');
    }

    /**
     * Update user role
     */
    public function updateRole(Request $request, User $user)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'role' => ['required', Rule::in(['admin', 'member', 'user'])],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Prevent users from removing their own admin role
        if ($user->id === auth()->id() && $user->isAdmin() && $request->role !== 'admin') {
            return back()->with('error', 'You cannot remove your own admin privileges.');
        }

        // Update the user role
        $user->update(['role' => $request->role]);

        return back()->with('success', "User role updated successfully to {$request->role}.");
    }

    /**
     * Show user details
     */
    public function show(User $user)
    {
        return view('admin.users.show', compact('user'));
    }

    /**
     * Show the form for editing a user
     */
    public function edit(User $user)
    {
        return view('admin.users.edit', compact('user'));
    }

    /**
     * Update the specified user
     */
    public function update(Request $request, User $user)
    {
        // Build validation rules
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'username' => ['nullable', 'string', 'max:255', 'alpha_dash', Rule::unique('users')->ignore($user->id)],
            'role' => ['required', Rule::in(['admin', 'member', 'user'])],
            'account_status' => ['required', Rule::in(['active', 'locked'])],
            'reset_failed_attempts' => ['boolean'],
        ];

        // Add password validation only if password is provided
        if ($request->filled('password')) {
            $rules['password'] = ['required', 'string', 'min:8', 'confirmed'];
        }

        // Validate the request
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Prevent users from removing their own admin role
        if ($user->id === auth()->id() && $user->isAdmin() && $request->role !== 'admin') {
            return back()->with('error', 'You cannot remove your own admin privileges.');
        }

        // Prevent users from locking their own account
        if ($user->id === auth()->id() && $request->account_status === 'locked') {
            return back()->with('error', 'You cannot lock your own account.');
        }

        // Prepare update data
        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'username' => $request->username,
            'role' => $request->role,
        ];

        // Add password to update data if provided
        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        // Update user data
        $user->update($updateData);

        // Handle account status changes
        if ($request->account_status === 'active' && $user->isLocked()) {
            $user->update([
                'locked_until' => null,
                'failed_login_attempts' => 0,
            ]);
        } elseif ($request->account_status === 'locked' && ! $user->isLocked()) {
            // Lock the user for 24 hours
            $user->update([
                'locked_until' => now()->addHours(24),
                'failed_login_attempts' => 5,
            ]);
        }

        // Reset failed login attempts if requested
        if ($request->boolean('reset_failed_attempts')) {
            $user->update([
                'failed_login_attempts' => 0,
                'locked_until' => null,
            ]);
        }

        return redirect()->route('admin.users.show', $user)
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified user from storage
     */
    public function destroy(User $user)
    {
        // Prevent users from deleting their own account
        if ($user->id === auth()->id()) {
            return back()->with('error', 'You cannot delete your own account.');
        }

        // Store user name for success message
        $userName = $user->name;

        // Hard delete the user (you can change this to soft delete if preferred)
        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', "User '{$userName}' has been deleted successfully.");
    }

    /**
     * Lock a user account
     */
    public function lock(User $user)
    {
        // Prevent users from locking their own account
        if ($user->id === auth()->id()) {
            return back()->with('error', 'You cannot lock your own account.');
        }

        // Lock the user for 24 hours
        $user->update([
            'locked_until' => now()->addHours(24),
            'failed_login_attempts' => 5, // Set to max attempts
        ]);

        return back()->with('success', "User '{$user->name}' has been locked for 24 hours.");
    }

    /**
     * Unlock a user account
     */
    public function unlock(User $user)
    {
        // Unlock the user
        $user->update([
            'locked_until' => null,
            'failed_login_attempts' => 0,
        ]);

        return back()->with('success', "User '{$user->name}' has been unlocked successfully.");
    }
}
