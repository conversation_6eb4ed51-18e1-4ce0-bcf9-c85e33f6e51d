<?php

namespace App\Http\Controllers;

use App\Mail\ReservationUpdated;
use App\Models\Field;
use App\Models\Reservation;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class CalendarController extends Controller
{
    /**
     * Display the calendar interface
     */
    public function index()
    {
        $fields = Field::active()->orderBy('name')->get();

        return view('calendar.index', compact('fields'));
    }

    /**
     * Get calendar events (AJAX endpoint for FullCalendar)
     */
    public function events(Request $request)
    {
        try {
            $start = $request->start;
            $end = $request->end;
            $fieldId = $request->field_id;

            $query = Reservation::with(['field', 'user'])
                ->where('booking_date', '>=', Carbon::parse($start)->toDateString())
                ->where('booking_date', '<=', Carbon::parse($end)->toDateString())
                ->whereIn('status', ['Pending', 'Confirmed', 'Completed']);

            // Filter by field if specified
            if ($fieldId) {
                $query->where('field_id', $fieldId);
            }

            // Filter by user role
            if (auth()->user()->isClient()) {
                // Clients can only see their own reservations
                $query->where('user_id', auth()->id());
            } elseif (auth()->user()->isMember()) {
                // Members can see all confirmed reservations + their own
                $query->where(function ($q) {
                    $q->where('status', 'Confirmed')
                        ->orWhere('user_id', auth()->id());
                });
            }
            // Admins can see all reservations (no additional filter)

            $reservations = $query->get();

            $events = [];
            foreach ($reservations as $reservation) {
                // Create datetime strings for FullCalendar (normalize to ISO 8601 with seconds)
                $startTimeFormatted = Carbon::parse((string) $reservation->start_time)->format('H:i:s');
                $endTimeFormatted = Carbon::parse((string) $reservation->end_time)->format('H:i:s');
                $dateStr = $reservation->booking_date->format('Y-m-d');

                $startDateTime = $dateStr.'T'.$startTimeFormatted; // e.g., 2025-08-10T16:00:00
                $endDateTime = $dateStr.'T'.$endTimeFormatted;     // e.g., 2025-08-10T17:30:00

                // Determine event color based on status
                $color = match ($reservation->status) {
                    'Pending' => '#fbbf24', // yellow
                    'Confirmed' => '#23b7e5', // light blue (secondary)
                    'Cancelled' => '#ef4444', // red
                    'Completed' => '#3b82f6', // blue
                    default => '#6b7280', // gray
                };

                // Determine if user can edit this reservation
                $canEdit = auth()->user()->isAdmin() ||
                          ($reservation->user_id === auth()->id() && $reservation->canBeCancelled());

                $events[] = [
                    'id' => $reservation->id,
                    'title' => $reservation->field->name.' - '.$reservation->customer_display_name,
                    'start' => $startDateTime,
                    'end' => $endDateTime,
                    'color' => $color,
                    'textColor' => '#ffffff',
                    'extendedProps' => [
                        'reservation_id' => $reservation->id,
                        'field_id' => $reservation->field_id,
                        'field_name' => $reservation->field->name,
                        'customer_name' => $reservation->customer_display_name,
                        'status' => $reservation->status,
                        'total_cost' => number_format($reservation->total_cost, 2),
                        'duration' => $reservation->duration_hours,
                        'can_edit' => $canEdit,
                        'special_requests' => $reservation->special_requests,
                    ],
                    'url' => route('reservations.show', $reservation),
                ];
            }

            return response()->json($events);

        } catch (\Exception $e) {
            Log::error('Calendar events error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json(['error' => 'Failed to fetch calendar events'], 500);
        }
    }

    /**
     * Check availability for a specific time slot (AJAX endpoint)
     */
    public function checkAvailability(Request $request)
    {
        $fieldId = $request->field_id;
        $date = $request->date;
        $startTime = $request->start_time;
        $duration = $request->duration;

        if (! $fieldId || ! $date || ! $startTime || ! $duration) {
            return response()->json(['available' => false, 'message' => 'Missing required parameters']);
        }

        $field = Field::find($fieldId);
        if (! $field) {
            return response()->json(['available' => false, 'message' => 'Field not found']);
        }

        // Calculate end time
        $startTimeCarbon = Carbon::createFromFormat('H:i', $startTime);
        $endTimeCarbon = $startTimeCarbon->copy()->addHours((int) $duration);
        $endTime = $endTimeCarbon->format('H:i');

        // Check business hours
        if ($startTimeCarbon->hour < 8 || $endTimeCarbon->hour > 22) {
            return response()->json([
                'available' => false,
                'message' => 'Bookings must be between 8:00 AM and 10:00 PM',
            ]);
        }

        // Check if date is in the past
        if (Carbon::parse($date)->isPast()) {
            return response()->json([
                'available' => false,
                'message' => 'Cannot book in the past',
            ]);
        }

        // Check field availability
        $available = $field->isAvailable($date, $startTime, $endTime);

        if ($available) {
            $totalCost = $field->hourly_rate * (int) $duration;

            return response()->json([
                'available' => true,
                'message' => 'Time slot is available',
                'total_cost' => number_format($totalCost, 2),
                'hourly_rate' => number_format($field->hourly_rate, 2),
            ]);
        } else {
            return response()->json([
                'available' => false,
                'message' => 'Time slot is not available',
            ]);
        }
    }

    // Calendar reservation date update
    public function updateReservationDate(Request $request, $id)
    {
        $request->validate([
            'start' => 'required|date',
            'end' => 'nullable|date',
        ]);

        $reservation = Reservation::findOrFail($id);

        // Check permission
        if (! auth()->user()->isAdmin() && auth()->id() !== $reservation->user_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        try {
            DB::beginTransaction();

            // Avoid timezone drift: take the date component from the incoming ISO string
            $startDateTimeStr = (string) $request->start;
            $reservation->booking_date = substr($startDateTimeStr, 0, 10);
            $reservation->save();

            DB::commit();

            // Send notification emails
            Mail::to($reservation->user->email)->send(new ReservationUpdated($reservation));

            // Optional: Send to customer if separate email
            if ($reservation->customer_email) {
                Mail::to($reservation->customer_email)->send(new ReservationUpdated($reservation));
            }

            // Optional: Send to admin(s)
            Mail::to('<EMAIL>')->send(new ReservationUpdated($reservation));

            return response()->json(['message' => 'Reservation updated successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update reservation from calendar drag', [
                'error' => $e->getMessage(),
            ]);

            return response()->json(['message' => 'Failed to update reservation'], 500);
        }
    }
}
