<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Field;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BookingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Booking::with(['field', 'user']);

        // Filter by user role
        if (auth()->user()->isClient()) {
            // Clients can only see their own bookings
            $query->where('user_id', auth()->id());
        } elseif (auth()->user()->isMember()) {
            // Members can see their own bookings
            // $query->where('user_id', auth()->id());
        }
        // Admins can see all bookings (no additional filter)

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('booking_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('booking_date', '<=', $request->date_to);
        }

        // Filter by field
        if ($request->filled('field_id')) {
            $query->where('field_id', $request->field_id);
        }

        $bookings = $query->orderBy('booking_date', 'desc')
            ->orderBy('start_time', 'desc')
            ->paginate(15);

        $fields = Field::active()->orderBy('name')->get();

        return view('bookings.index', compact('bookings', 'fields'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $fields = Field::active()->orderBy('name')->get();

        // Pre-fill form if coming from calendar
        $selectedField = $request->field_id ? Field::find($request->field_id) : null;
        $selectedDate = $request->date;
        $selectedTime = $request->time;

        return view('bookings.create', compact('fields', 'selectedField', 'selectedDate', 'selectedTime'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'field_id' => ['required', 'exists:fields,id'],
            'booking_date' => ['required', 'date', 'after_or_equal:today'],
            'start_time' => ['required', 'regex:/^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9](?::[0-5][0-9])?$/'],
            'duration_hours' => ['required', 'integer', 'min:1'],
            'customer_name' => ['nullable', 'string', 'max:255'],
            'customer_email' => ['nullable', 'email', 'max:255'],
            'customer_phone' => ['nullable', 'string', 'max:20'],
            'special_requests' => ['nullable', 'string', 'max:1000'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $field = Field::findOrFail($request->field_id);

        // Calculate end time
        $startTime = Carbon::parse($request->start_time);
        $endTime = $startTime->copy()->addHours((int) $request->duration_hours);

        // Validate duration
        if (! $field->isValidDuration($request->duration_hours)) {
            return back()->withErrors([
                'duration_hours' => 'Duration must be between '.$field->min_booking_hours.
                                   ' and '.$field->max_booking_hours.' hours for this field.',
            ])->withInput();
        }

        // Validate business hours (8 AM - 10 PM)
        if ($startTime->hour < 8 || $endTime->hour > 22) {
            return back()->withErrors(['start_time' => 'Bookings must be between 8:00 AM and 10:00 PM.'])->withInput();
        }

        // Check availability
        if (! $field->isAvailable($request->booking_date, $request->start_time, $endTime->format('H:i'))) {
            return back()->withErrors(['start_time' => 'The selected time slot is not available.'])->withInput();
        }

        // Calculate total cost
        $totalCost = $field->hourly_rate * (int) $request->duration_hours;

        // Determine booking status based on user role
        $status = 'Pending';
        if (auth()->user()->isAdmin()) {
            $status = 'Confirmed'; // Admins can directly confirm bookings
        } elseif (auth()->user()->isClient()) {
            $status = 'Pending'; // Clients need approval
        } else {
            $status = 'Confirmed'; // Members get auto-confirmed
        }

        $booking = Booking::create([
            'field_id' => $request->field_id,
            'user_id' => auth()->id(),
            'booked_by' => auth()->user()->isAdmin() ? auth()->id() : null,
            'booking_date' => $request->booking_date,
            'start_time' => $request->start_time,
            'end_time' => $endTime->format('H:i'),
            'duration_hours' => (int) $request->duration_hours,
            'total_cost' => $totalCost,
            'status' => $status,
            'customer_name' => $request->customer_name,
            'customer_email' => $request->customer_email,
            'customer_phone' => $request->customer_phone,
            'special_requests' => $request->special_requests,
            'confirmed_at' => $status === 'Confirmed' ? now() : null,
        ]);

        $message = $status === 'Confirmed'
            ? 'Booking created and confirmed successfully!'
            : 'Booking created successfully! It is pending approval.';

        return redirect()->route('bookings.show', $booking)->with('success', $message);
    }

    /**
     * Display the specified resource.
     */
    public function show(Booking $booking)
    {
        // Authorization check
        if (! auth()->user()->isAdmin() && $booking->user_id !== auth()->id()) {
            abort(403, 'You can only view your own bookings.');
        }

        $booking->load(['field', 'user', 'bookedBy']);

        return view('bookings.show', compact('booking'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Booking $booking)
    {
        // Authorization check
        if (! auth()->user()->isAdmin() && $booking->user_id !== auth()->id()) {
            abort(403, 'You can only edit your own bookings.');
        }

        // Can't edit past bookings or completed/cancelled bookings
        if ($booking->booking_date->isPast() || in_array($booking->status, ['Completed', 'Cancelled'])) {
            return back()->with('error', 'This booking cannot be edited.');
        }

        $fields = Field::active()->orderBy('name')->get();

        return view('bookings.edit', compact('booking', 'fields'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Booking $booking)
    {
        // Authorization check
        if (! auth()->user()->isAdmin() && $booking->user_id !== auth()->id()) {
            abort(403, 'You can only edit your own bookings.');
        }

        $validator = Validator::make($request->all(), [
            'field_id' => ['required', 'exists:fields,id'],
            'booking_date' => ['required', 'date', 'after_or_equal:today'],
            'start_time' => ['required', 'regex:/^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9](?::[0-5][0-9])?$/'],
            'duration_hours' => ['required', 'integer', 'min:1'],
            'customer_name' => ['nullable', 'string', 'max:255'],
            'customer_email' => ['nullable', 'email', 'max:255'],
            'customer_phone' => ['nullable', 'string', 'max:20'],
            'special_requests' => ['nullable', 'string', 'max:1000'],
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $field = Field::findOrFail($request->field_id);

        // Calculate end time
        $startTime = Carbon::parse($request->start_time);
        $endTime = $startTime->copy()->addHours((int) $request->duration_hours);

        // Validate duration
        if (! $field->isValidDuration($request->duration_hours)) {
            return back()->withErrors([
                'duration_hours' => 'Duration must be between '.$field->min_booking_hours.
                                   ' and '.$field->max_booking_hours.' hours for this field.',
            ])->withInput();
        }

        // Validate business hours
        if ($startTime->hour < 8 || $endTime->hour > 22) {
            return back()->withErrors(['start_time' => 'Bookings must be between 8:00 AM and 10:00 PM.'])->withInput();
        }

        // Check availability (excluding current booking)
        if (! $field->isAvailable($request->booking_date, $request->start_time, $endTime->format('H:i'), $booking->id)) {
            return back()->withErrors(['start_time' => 'The selected time slot is not available.'])->withInput();
        }

        // Calculate total cost
        $totalCost = $field->hourly_rate * (int) $request->duration_hours;

        $booking->update([
            'field_id' => $request->field_id,
            'booking_date' => $request->booking_date,
            'start_time' => $request->start_time,
            'end_time' => $endTime->format('H:i'),
            'duration_hours' => (int) $request->duration_hours,
            'total_cost' => $totalCost,
            'customer_name' => $request->customer_name,
            'customer_email' => $request->customer_email,
            'customer_phone' => $request->customer_phone,
            'special_requests' => $request->special_requests,
        ]);

        return redirect()->route('bookings.show', $booking)->with('success', 'Booking updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Booking $booking)
    {
        // Authorization check
        if (! auth()->user()->isAdmin() && $booking->user_id !== auth()->id()) {
            abort(403, 'You can only delete your own bookings.');
        }

        $booking->delete();

        return redirect()->route('bookings.index')->with('success', 'Booking deleted successfully.');
    }

    /**
     * Confirm a booking (Admin only)
     */
    public function confirm(Booking $booking)
    {
        if (! auth()->user()->isAdmin()) {
            abort(403, 'Only administrators can confirm bookings.');
        }

        if ($booking->status !== 'Pending') {
            return back()->with('error', 'Only pending bookings can be confirmed.');
        }

        $booking->update([
            'status' => 'Confirmed',
            'confirmed_at' => now(),
        ]);

        return back()->with('success', 'Booking confirmed successfully.');
    }

    /**
     * Cancel a booking
     */
    public function cancel(Booking $booking)
    {
        // Authorization check
        if (! auth()->user()->isAdmin() && $booking->user_id !== auth()->id()) {
            abort(403, 'You can only cancel your own bookings.');
        }

        if (! $booking->canBeCancelled()) {
            return back()->with('error', 'This booking cannot be cancelled.');
        }

        $booking->update([
            'status' => 'Cancelled',
            'cancelled_at' => now(),
        ]);

        return back()->with('success', 'Booking cancelled successfully.');
    }
}
