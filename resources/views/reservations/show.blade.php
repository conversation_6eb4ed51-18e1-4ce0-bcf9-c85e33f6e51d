@extends('layouts.admin')

@section('title', 'Reservation Details - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Reservation Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('reservations.index') }}">Reservations</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Reservation #{{ $reservation->id }}</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="ti ti-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="ti ti-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Reservation Details -->
    <div class="row">
        <div class="col-xl-8">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="ti ti-calendar-event me-2"></i>Reservation #{{ $reservation->id }} Details
                        <span
                            class="badge bg-{{ $reservation->status_color }}-transparent text-{{ $reservation->status_color }} ms-2">
                            {{ $reservation->status }}
                        </span>
                    </div>
                    <div class="d-flex gap-2">
                        @if ($reservation->canBeModified())
                            <a href="{{ route('reservations.edit', $reservation) }}" class="btn btn-warning btn-sm">
                                <i class="ti ti-edit me-1"></i>Edit
                            </a>
                        @endif
                        @if ($reservation->canBeCancelled())
                            <form method="POST" action="{{ route('reservations.cancel', $reservation) }}" class="d-inline"
                                onsubmit="return confirm('Are you sure you want to cancel this reservation?')">
                                @csrf
                                <button type="submit" class="btn btn-danger btn-sm">
                                    <i class="ti ti-x me-1"></i>Cancel
                                </button>
                            </form>
                        @endif
                        <a href="{{ route('reservations.index') }}" class="btn btn-secondary btn-sm">
                            <i class="ti ti-arrow-left me-1"></i>Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row gy-4">
                        <!-- Field Information -->
                        <div class="col-xl-6">
                            <div class="card custom-card shadow-none border">
                                <div class="card-header">
                                    <div class="card-title">Field Information</div>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="me-3">
                                            <span class="avatar avatar-lg bg-primary-transparent">
                                                <i class="ti ti-building-stadium fs-20"></i>
                                            </span>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">{{ $reservation->field->name }}</h5>
                                            <span class="badge bg-info-transparent">{{ $reservation->field->type }}</span>
                                        </div>
                                    </div>

                                    <div class="row gy-2">
                                        <div class="col-4">
                                            <small class="text-muted">Capacity</small>
                                            <div class="fw-semibold">{{ $reservation->field->capacity }} people</div>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted"> Day Hourly Rate</small>
                                            <div class="fw-semibold">
                                                XCG {{ number_format($reservation->field->hourly_rate, 2) }}</div>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted"> Night Hourly Rate</small>
                                            <div class="fw-semibold">
                                                XCG {{ number_format($reservation->field->night_hourly_rate, 2) }}</div>
                                        </div>
                                        <div class="col-12">
                                            <small class="text-muted">Description</small>
                                            <div class="fw-semibold">
                                                {{ $reservation->field->description ?: 'No description available' }}</div>
                                        </div>
                                        <!-- @if ($reservation->field->amenities)
                                                                                                <div class="col-12">
                                                                                                    <small class="text-muted">Amenities</small>
                                                                                                    <div class="mt-1">
                                                                                                        @foreach ($reservation->field->amenities as $amenity)
    <span
                                                                                                                class="badge bg-light text-dark me-1">{{ ucfirst($amenity->name) }}</span>
    @endforeach
                                                                                                    </div>
                                                                                                </div>
                                                                                            @endif -->

                                        <!-- utilities -->
                                        @if ($reservation->utilities && $reservation->utilities->count())
                                            <div class="col-12 mt-3">
                                                <small class="text-muted">Utilities</small>
                                                <div class="table-responsive mt-2">
                                                    <table class="table table-sm table-bordered">
                                                        <thead class="table-light">
                                                            <tr>
                                                                <th>Utility</th>
                                                                <th>Quantity</th>
                                                                <th>Rate</th>
                                                                <th>Total Cost</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            @foreach ($reservation->utilities as $utility)
                                                                <tr>
                                                                    <td>{{ $utility->name }}</td>
                                                                    <td>{{ $utility->pivot->hours }}</td>
                                                                    <td>XCG {{ number_format($utility->pivot->rate, 2) }}
                                                                    </td>
                                                                    <td>XCG {{ number_format($utility->pivot->cost, 2) }}
                                                                    </td>
                                                                </tr>
                                                            @endforeach
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        @endif
                                        <!-- end Utilities -->



                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Reservation Schedule -->
                        <div class="col-xl-6">
                            <div class="card custom-card shadow-none border">
                                <div class="card-header">
                                    <div class="card-title">Schedule & Cost</div>
                                </div>
                                <div class="card-body">
                                    <div class="row gy-3">
                                        <div class="col-12">
                                            <div class="d-flex align-items-center">
                                                <i class="ti ti-calendar me-2 text-primary"></i>
                                                <div>
                                                    <small class="text-muted">Date</small>
                                                    <div class="fw-semibold">
                                                        {{ $reservation->booking_date->format('l, F d, Y') }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="d-flex align-items-center">
                                                <i class="ti ti-clock me-2 text-info"></i>
                                                <div>
                                                    <small class="text-muted">Time</small>
                                                    <div class="fw-semibold">{{ $reservation->time_range }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="d-flex align-items-center">
                                                <i class="ti ti-hourglass me-2 text-warning"></i>
                                                <div>
                                                    <small class="text-muted">Duration</small>
                                                    <div class="fw-semibold">{{ $reservation->duration_hours }}
                                                        {{ Str::plural('hour', $reservation->duration_hours) }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="alert alert-success">
                                                <h6 class="fw-semibold mb-3">Reservation Cost Breakdown</h6>

                                                @php
                                                    use App\Services\ReservationCostService;
                                                    $costService = new ReservationCostService();
                                                    $costBreakdown = $costService->getCostBreakdown(
                                                        $reservation->field,
                                                        $reservation->duration_hours,
                                                        $reservation->start_time
                                                    );
                                                    $utilityTotal = $reservation->utilities->sum('pivot.cost');
                                                @endphp

                                                <!-- Field Cost Breakdown -->
                                                <div class="mb-3">
                                                    <div class="fw-semibold mb-2">Field Cost:</div>
                                                    @if($costBreakdown['rate_breakdown'] && ($costBreakdown['rate_breakdown']['day_hours'] > 0 || $costBreakdown['rate_breakdown']['night_hours'] > 0))
                                                        <!-- Day/Night Rate Breakdown -->
                                                        @if($costBreakdown['rate_breakdown']['day_hours'] > 0)
                                                            <div class="fs-12 text-muted">
                                                                Day Rate: {{ $costBreakdown['rate_breakdown']['day_hours'] }} hours × XCG {{ number_format($reservation->field->hourly_rate, 2) }} = XCG {{ number_format($costBreakdown['rate_breakdown']['day_cost'], 2) }}
                                                            </div>
                                                        @endif
                                                        @if($costBreakdown['rate_breakdown']['night_hours'] > 0)
                                                            <div class="fs-12 text-muted">
                                                                Night Rate: {{ $costBreakdown['rate_breakdown']['night_hours'] }} hours × XCG {{ number_format($reservation->field->night_hourly_rate, 2) }} = XCG {{ number_format($costBreakdown['rate_breakdown']['night_cost'], 2) }}
                                                            </div>
                                                        @endif
                                                    @else
                                                        <!-- Simple Rate Display -->
                                                        <div class="fs-12 text-muted">
                                                            {{ $reservation->duration_hours }} hours × XCG {{ number_format($reservation->field->hourly_rate, 2) }} = XCG {{ number_format($costBreakdown['subtotal'], 2) }}
                                                        </div>
                                                    @endif
                                                    <div class="fs-13 fw-semibold mt-1">
                                                        Field Total: XCG {{ number_format($costBreakdown['subtotal'], 2) }}
                                                    </div>
                                                </div>

                                                <!-- Utility Cost Breakdown -->
                                                @if($reservation->utilities->count() > 0)
                                                    <div class="mb-3">
                                                        <div class="fw-semibold mb-2">Utility Costs:</div>
                                                        @foreach ($reservation->utilities as $utility)
                                                            <div class="fs-12 text-muted">
                                                                {{ $utility->name }}: {{ $utility->pivot->hours }} hours × XCG {{ number_format($utility->pivot->rate, 2) }} = XCG {{ number_format($utility->pivot->cost, 2) }}
                                                            </div>
                                                        @endforeach
                                                        <div class="fs-13 fw-semibold mt-1">
                                                            Utility Total: XCG {{ number_format($utilityTotal, 2) }}
                                                        </div>
                                                    </div>
                                                @endif

                                                <!-- Total Cost -->
                                                <div class="border-top pt-3">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <span class="fw-bold fs-16">Total Cost:</span>
                                                            <div class="fs-11 text-muted mt-1">
                                                                <i class="ti ti-shield-check me-1"></i>Calculated securely by server
                                                            </div>
                                                        </div>
                                                        <div class="h4 mb-0 text-success">
                                                            XCG {{ number_format($reservation->total_cost, 2) }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Information -->
                        <div class="col-xl-12">
                            <div class="card custom-card shadow-none border">
                                <div class="card-header">
                                    <div class="card-title">Customer Information</div>
                                </div>
                                <div class="card-body">
                                    <div class="row gy-3">
                                        <div class="col-md-4">
                                            <small class="text-muted">Customer Name</small>
                                            <div class="fw-semibold">{{ $reservation->customer_display_name }}</div>
                                        </div>
                                        <div class="col-md-4">
                                            <small class="text-muted">Email</small>
                                            <div class="fw-semibold">{{ $reservation->customer_email ?: 'Not provided' }}
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <small class="text-muted">Phone</small>
                                            <div class="fw-semibold">{{ $reservation->customer_phone ?: 'Not provided' }}
                                            </div>
                                        </div>
                                        @if ($reservation->special_requests)
                                            <div class="col-12">
                                                <small class="text-muted">Special Requests</small>
                                                <div class="fw-semibold">{{ $reservation->special_requests }}</div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reservation Status & Timeline -->
        <div class="col-xl-4">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Reservation Status</div>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">Reservation Created</h6>
                                <p class="timeline-text">{{ $reservation->created_at->format('M d, Y H:i') }}</p>
                            </div>
                        </div>

                        @if ($reservation->confirmed_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">Confirmed</h6>
                                    <p class="timeline-text">{{ $reservation->confirmed_at->format('M d, Y H:i') }}</p>
                                </div>
                            </div>
                        @endif

                        @if ($reservation->cancelled_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-danger"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">Cancelled</h6>
                                    <p class="timeline-text">{{ $reservation->cancelled_at->format('M d, Y H:i') }}</p>
                                </div>
                            </div>
                        @endif

                        @if ($reservation->status === 'Confirmed' && $reservation->booking_date->isFuture())
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">Scheduled</h6>
                                    <p class="timeline-text">{{ $reservation->formatted_date_time }}</p>
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Quick Actions -->
                    <div class="mt-4">
                        <h6 class="fw-semibold mb-3">Quick Actions</h6>
                        <div class="d-grid gap-2">
                            @if ($reservation->canBeModified())
                                <a href="{{ route('reservations.edit', $reservation) }}" class="btn btn-warning btn-sm">
                                    <i class="ti ti-edit me-1"></i>Modify Reservation
                                </a>
                            @endif
                            @if ($reservation->canBeCancelled())
                                <form method="POST" action="{{ route('reservations.cancel', $reservation) }}"
                                    onsubmit="return confirm('Are you sure you want to cancel this reservation?')">
                                    @csrf
                                    <button type="submit" class="btn btn-danger btn-sm w-100">
                                        <i class="ti ti-x me-1"></i>Cancel Reservation
                                    </button>
                                </form>
                            @endif
                            <a href="{{ route('reservations.create', ['field_id' => $reservation->field_id]) }}"
                                class="btn btn-primary btn-sm">
                                <i class="ti ti-plus me-1"></i>Book Same Field
                            </a>
                        </div>
                    </div>

                    <!-- Important Notes -->
                    <div class="mt-4">
                        <div class="alert alert-info">
                            <h6 class="fw-semibold">Important Notes</h6>
                            <ul class="mb-0 fs-12">
                                <li>Please arrive 15 minutes before your scheduled time</li>
                                <li>Bring valid ID for verification</li>
                                <li>Cancellations must be made 24 hours in advance</li>
                                @if ($reservation->booking_date->isToday())
                                    <li><strong>Your reservation is today!</strong></li>
                                @elseif($reservation->booking_date->isTomorrow())
                                    <li><strong>Your reservation is tomorrow!</strong></li>
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        .timeline-marker {
            position: absolute;
            left: -25px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #e9ecef;
        }

        .timeline-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .timeline-text {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 0;
        }
    </style>
@endpush
