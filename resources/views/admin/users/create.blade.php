@extends('layouts.admin')

@section('title', 'Create User - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">User Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.users.index') }}">Users</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Create User</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <!-- Create User Form -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">Create New User</div>
                    <div>
                        <a href="{{ route('admin.users.index') }}" class="btn btn-secondary btn-sm">
                            <i class="ti ti-arrow-left me-1"></i>Back to Users
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.users.store') }}">
                        @csrf
                        <div class="row gy-4">
                            <!-- Basic Information -->
                            <div class="col-xl-6">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Basic Information</div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <div class="col-xl-12">
                                                <label for="name" class="form-label">Full Name <span
                                                        class="text-danger">*</span></label>
                                                <input type="text"
                                                    class="form-control @error('name') is-invalid @enderror" id="name"
                                                    name="name" value="{{ old('name') }}" placeholder="Enter full name"
                                                    required>
                                                @error('name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                            <div class="col-xl-12">
                                                <label for="email" class="form-label">Email Address <span
                                                        class="text-danger">*</span></label>
                                                <input type="email"
                                                    class="form-control @error('email') is-invalid @enderror" id="email"
                                                    name="email" value="{{ old('email') }}"
                                                    placeholder="Enter email address" required>
                                                @error('email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                            <div class="col-xl-12">
                                                <label for="username" class="form-label">Username</label>
                                                <input type="text"
                                                    class="form-control @error('username') is-invalid @enderror"
                                                    id="username" name="username" value="{{ old('username') }}"
                                                    placeholder="Enter username (optional)">
                                                @error('username')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <div class="form-text">Username is optional. If not provided, email will be
                                                    used for login.</div>
                                            </div>
                                            <div class="col-xl-12">
                                                <label for="role" class="form-label">User Role <span
                                                        class="text-danger">*</span></label>
                                                <select class="form-select @error('role') is-invalid @enderror"
                                                    id="role" name="role" required>
                                                    <option value="">Select Role</option>
                                                    <option value="admin" {{ old('role') === 'admin' ? 'selected' : '' }}>
                                                        Admin</option>
                                                    <option value="member"
                                                        {{ old('role') === 'member' ? 'selected' : '' }}>Member
                                                    </option>
                                                    <!-- <option value="user" {{ old('role') === 'user' ? 'selected' : '' }}>
                                                            User</option> -->
                                                </select>
                                                @error('role')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Password Information -->
                            <div class="col-xl-6">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Password Information</div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <div class="col-xl-12">
                                                <label for="password" class="form-label">Password <span
                                                        class="text-danger">*</span></label>
                                                <div class="input-group">
                                                    <input type="password"
                                                        class="form-control @error('password') is-invalid @enderror"
                                                        id="password" name="password" placeholder="Enter password"
                                                        required>
                                                    <button class="btn btn-light" type="button"
                                                        onclick="togglePassword('password', this)">
                                                        <i class="ri-eye-off-line align-middle"></i>
                                                    </button>
                                                    @error('password')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                                <div class="form-text">Password must be at least 8 characters long.</div>
                                            </div>
                                            <div class="col-xl-12">
                                                <label for="password_confirmation" class="form-label">Confirm Password <span
                                                        class="text-danger">*</span></label>
                                                <div class="input-group">
                                                    <input type="password"
                                                        class="form-control @error('password_confirmation') is-invalid @enderror"
                                                        id="password_confirmation" name="password_confirmation"
                                                        placeholder="Confirm password" required>
                                                    <button class="btn btn-light" type="button"
                                                        onclick="togglePassword('password_confirmation', this)">
                                                        <i class="ri-eye-off-line align-middle"></i>
                                                    </button>
                                                    @error('password_confirmation')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Role Information -->
                            <div class="col-xl-12">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Role Information</div>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-info">
                                            <h6 class="fw-semibold">Role Descriptions:</h6>
                                            <ul class="mb-0">
                                                <li><strong>Admin:</strong> Full access to all system features including
                                                    user management, field management, and system settings.</li>
                                                <li><strong>Member:</strong> Can create and manage their own bookings,
                                                    view calendar, and access standard features.</li>
                                                <li><strong>User:</strong> Limited access focused on viewing and basic
                                                    booking functionality.</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="col-xl-12">
                                <div class="d-flex gap-2 justify-content-end">
                                    <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-plus me-1"></i>Create User
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function togglePassword(fieldId, button) {
            const field = document.getElementById(fieldId);
            const icon = button.querySelector('i');

            if (field.type === 'password') {
                field.type = 'text';
                icon.className = 'ri-eye-line align-middle';
            } else {
                field.type = 'password';
                icon.className = 'ri-eye-off-line align-middle';
            }
        }
    </script>
@endsection
