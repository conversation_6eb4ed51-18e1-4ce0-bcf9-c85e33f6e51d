@extends('layouts.admin')

@section('title', 'Admin Dashboard - Field Management System')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Admin Dashboard</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Admin Dashboard</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- Welcome Section -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-lg bg-primary-transparent">
                                <i class="ti ti-shield-check fs-18"></i>
                            </span>
                        </div>
                        <div>
                            <h5 class="fw-semibold mb-1">Welcome back, {{ auth()->user()->name ?? 'Admin' }}!</h5>
                            <p class="text-muted mb-0">You have full administrative access to the Field Management System.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Management Sections -->
    <div class="row gy-4">
        <!-- Reservations Management Section -->
        <div class="col-xl-4">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">Reservations</div>
                    <a href="{{ route('reservations.index') }}" class="btn btn-info btn-sm">
                        <i class="ti ti-calendar-check me-1"></i>View All
                    </a>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Manage property reservations, bookings, and availability.</p>
                    <div class="row gy-2">
                        <div class="col-12">
                            <a href="{{ route('reservations.index') }}" class="btn btn-outline-info btn-sm w-100">
                                <i class="ti ti-list me-1"></i>Reservations
                            </a>
                        </div>
                        <div class="col-12">
                            <a href="{{ route('reservations.create') }}" class="btn btn-outline-success btn-sm w-100">
                                <i class="ti ti-plus me-1"></i>New Reservation
                            </a>
                        </div>
                        <div class="col-12">
                            <a href="{{ route('calendar.index') }}" class="btn btn-outline-warning btn-sm w-100">
                                <i class="ti ti-calendar me-1"></i>Calendar
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Management Section -->
        <div class="col-xl-4">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">User Management</div>
                    <a href="{{ route('admin.users.index') }}" class="btn btn-primary btn-sm">
                        <i class="ti ti-users me-1"></i>Manage All Users
                    </a>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Comprehensive user management and role administration.</p>
                    <div class="row gy-2">
                        <div class="col-12">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-outline-primary btn-sm w-100">
                                <i class="ti ti-list me-1"></i>View All Users
                            </a>
                        </div>
                        <div class="col-12">
                            <a href="{{ route('admin.users.create') }}" class="btn btn-outline-success btn-sm w-100">
                                <i class="ti ti-user-plus me-1"></i>Create New User
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Overview Section -->
        <div class="col-xl-4">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">System Overview</div>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Quick overview of system status and recent activity.</p>
                    <div class="row gy-3">
                        <div class="col-6">
                            <div class="text-center">
                                <h5 class="fw-bold text-primary mb-1">
                                    {{ \App\Models\User::whereDate('created_at', today())->count() }}</h5>
                                <p class="text-muted fs-12 mb-0">New Users Today</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h5 class="fw-bold text-success mb-1">
                                    {{ \App\Models\User::whereNotNull('email_verified_at')->count() }}</h5>
                                <p class="text-muted fs-12 mb-0">Verified Users</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row gy-4">
        <!-- Total Users Card -->
        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-primary-transparent">
                                <i class="ti ti-users fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <h6 class="fw-semibold mb-1">Total Users</h6>
                            <h4 class="fw-bold text-primary mb-0">{{ \App\Models\User::count() }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Admin Users Card -->
        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-danger-transparent">
                                <i class="ti ti-shield-check fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <h6 class="fw-semibold mb-1">Administrators</h6>
                            <h4 class="fw-bold text-danger mb-0">{{ \App\Models\User::where('role', 'admin')->count() }}
                            </h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Member Card -->
        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-success-transparent">
                                <i class="ti ti-user fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <h6 class="fw-semibold mb-1">Members</h6>
                            <h4 class="fw-bold text-success mb-0">
                                {{ \App\Models\User::where('role', 'member')->count() }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Client Users Card -->
        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-warning-transparent">
                                <i class="ti ti-briefcase fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <h6 class="fw-semibold mb-1">Users</h6>
                            <h4 class="fw-bold text-warning mb-0">{{ \App\Models\User::where('role', 'user')->count() }}
                            </h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-----Weekly Reservations Table------>
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="ti ti-list me-2"></i>Current week Reservations
                    </div>
                </div>
                @php
                    use App\Models\Reservation;
                    use Carbon\Carbon;
                    $query = Reservation::with('field', 'user');
                    $reservations = $query
                        ->whereIn('status', ['Pending', 'Confirmed', 'Cancelled']) // adjust statuses as needed
                        ->whereBetween('booking_date', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])
                        ->orderBy('booking_date', 'desc')
                        ->orderBy('start_time', 'desc')
                        ->paginate(10);
                @endphp

                <div class="card-body">
                    @if ($reservations->count() > 0)

                        <div class="table-responsive">
                            <table class="table text-nowrap table-hover">
                                <thead>
                                    <tr>
                                        <th>Field</th>
                                        <th>Date & Time</th>
                                        <th>Customer</th>
                                        <th>Cost</th>
                                        <th>Status</th>
                                        <!--<th>Actions</th>-->
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($reservations as $reservation)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-2">
                                                        <span class="avatar avatar-sm bg-light">
                                                            <i class="ti ti-building-stadium fs-12"></i>
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <span class="fw-semibold">{{ $reservation->field->name }}</span>
                                                        <br>
                                                        <small class="text-muted">{{ $reservation->field->type }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <span class="fw-semibold">{{ $reservation->booking_date->format('M d, Y') }}</span>
                                                    <br>
                                                    <small class="text-muted">{{ $reservation->time_range }}</small>
                                                    <br>
                                                    <span class="badge bg-primary-transparent text-primary">Duration: {{ $reservation->duration_hours }} {{ Str::plural('hour', $reservation->duration_hours) }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-semibold">{{ $reservation->customer_display_name }}</span>
                                            </td>
                                            <td>
                                                <span class="fw-semibold text-success">XCG
                                                    {{ number_format($reservation->total_cost, 2) }}</span>
                                            </td>
                                            <td>
                                                <span
                                                    class="badge bg-{{ $reservation->status_color }}-transparent text-{{ $reservation->status_color }}">
                                                    {{ $reservation->status }}
                                                </span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="booking-pagination">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="pagination-info">
                                    Showing {{ $reservations->firstItem() }} to {{ $reservations->lastItem() }}
                                    of {{ $reservations->total() }} reservations
                                </div>
                                <div class="admin-pagination">
                                    {{ $reservations->links() }}
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="mb-3">
                                <i class="ti ti-calendar-x fs-48 text-muted"></i>
                            </div>
                            <h5 class="text-muted">No Reservations Found</h5>
                            <p class="text-muted">You haven't made any reservations yet.</p>
                            <a href="{{ route('reservations.create') }}" class="btn btn-primary">
                                <i class="ti ti-plus me-1"></i>Make Your First Reservation
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>



    <!-----Next Week Reservations Table------>
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="ti ti-list me-2"></i>Next week Reservations
                    </div>
                </div>
                @php
                    // use App\Models\Reservation;
                    // use Carbon\Carbon;
                    $nextQuery = Reservation::with('field', 'user');
                    $nextWeekReservations = $nextQuery
                        ->whereIn('status', ['Pending', 'Confirmed', 'Cancelled'])
                        ->whereBetween('booking_date', [Carbon::now()->addWeek()->startOfWeek(), Carbon::now()->addWeek()->endOfWeek()])
                        ->orderBy('booking_date', 'desc')
                        ->orderBy('start_time', 'desc')
                        ->paginate(10);
                @endphp

                <div class="card-body">
                    @if ($nextWeekReservations->count() > 0)

                        <div class="table-responsive">
                            <table class="table text-nowrap table-hover">
                                <thead>
                                    <tr>
                                        <th>Field</th>
                                        <th>Date & Time</th>
                                        <th>Customer</th>
                                        <th>Cost</th>
                                        <th>Status</th>
                                        <!--<th>Actions</th>-->
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($nextWeekReservations as $reservation)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-2">
                                                        <span class="avatar avatar-sm bg-light">
                                                            <i class="ti ti-building-stadium fs-12"></i>
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <span class="fw-semibold">{{ $reservation->field->name }}</span>
                                                        <br>
                                                        <small class="text-muted">{{ $reservation->field->type }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <span class="fw-semibold">{{ $reservation->booking_date->format('M d, Y') }}</span>
                                                    <br>
                                                    <small class="text-muted">{{ $reservation->time_range }}</small>
                                                    <br>
                                                    <span class="badge bg-primary-transparent text-primary">Duration: {{ $reservation->duration_hours }} {{ Str::plural('hour', $reservation->duration_hours) }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-semibold">{{ $reservation->customer_display_name }}</span>
                                            </td>
                                            <td>
                                                <span class="fw-semibold text-success">XCG {{ number_format($reservation->total_cost, 2) }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $reservation->status_color }}-transparent text-{{ $reservation->status_color }}">
                                                    {{ $reservation->status }}
                                                </span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="booking-pagination">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="pagination-info">
                                    Showing {{ $nextWeekReservations->firstItem() }} to {{ $nextWeekReservations->lastItem() }} of {{ $nextWeekReservations->total() }} reservations
                                </div>
                                <div class="admin-pagination">
                                    {{ $nextWeekReservations->links() }}
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="mb-3">
                                <i class="ti ti-calendar-x fs-48 text-muted"></i>
                            </div>
                            <h5 class="text-muted">No Reservations Found</h5>
                            <p class="text-muted">You haven't made any reservations yet.</p>
                            <a href="{{ route('reservations.create') }}" class="btn btn-primary">
                                <i class="ti ti-plus me-1"></i>Make Your First Reservation
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection
