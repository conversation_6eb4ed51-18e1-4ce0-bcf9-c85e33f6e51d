<?php

namespace Tests\Feature\Admin\User;

use App\Http\Controllers\Admin\UserManagementController;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(UserManagementController::class)]
class UserManagementAuthorizationTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;

    protected User $memberUser;

    protected User $regularUser;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users with different roles
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);

        $this->memberUser = User::factory()->create([
            'role' => 'member',
            'name' => 'Member User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);

        $this->regularUser = User::factory()->create([
            'role' => 'user',
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);
    }

    #[Test]
    public function unauthenticated_users_cannot_access_admin_users_index()
    {
        $response = $this->get(route('admin.users.index'));

        $response->assertStatus(302);
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function unauthenticated_users_cannot_access_admin_users_create()
    {
        $response = $this->get(route('admin.users.create'));

        $response->assertStatus(302);
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function unauthenticated_users_cannot_access_admin_users_show()
    {
        $user = User::factory()->create();

        $response = $this->get(route('admin.users.show', $user));

        $response->assertStatus(302);
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function unauthenticated_users_cannot_access_admin_users_edit()
    {
        $user = User::factory()->create();

        $response = $this->get(route('admin.users.edit', $user));

        $response->assertStatus(302);
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function member_users_cannot_access_admin_users_index()
    {
        $response = $this->actingAs($this->memberUser)
            ->get(route('admin.users.index'));

        $response->assertStatus(403);
        $response->assertSee('Access denied. Admin privileges required.');
    }

    #[Test]
    public function member_users_cannot_access_admin_users_create()
    {
        $response = $this->actingAs($this->memberUser)
            ->get(route('admin.users.create'));

        $response->assertStatus(403);
        $response->assertSee('Access denied. Admin privileges required.');
    }

    #[Test]
    public function member_users_cannot_access_admin_users_show()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($this->memberUser)
            ->get(route('admin.users.show', $user));

        $response->assertStatus(403);
        $response->assertSee('Access denied. Admin privileges required.');
    }

    #[Test]
    public function member_users_cannot_access_admin_users_edit()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($this->memberUser)
            ->get(route('admin.users.edit', $user));

        $response->assertStatus(403);
        $response->assertSee('Access denied. Admin privileges required.');
    }

    #[Test]
    public function regular_users_cannot_access_admin_users_index()
    {
        $response = $this->actingAs($this->regularUser)
            ->get(route('admin.users.index'));

        $response->assertStatus(403);
        $response->assertSee('Access denied. Admin privileges required.');
    }

    #[Test]
    public function regular_users_cannot_access_admin_users_create()
    {
        $response = $this->actingAs($this->regularUser)
            ->get(route('admin.users.create'));

        $response->assertStatus(403);
        $response->assertSee('Access denied. Admin privileges required.');
    }

    #[Test]
    public function regular_users_cannot_access_admin_users_show()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($this->regularUser)
            ->get(route('admin.users.show', $user));

        $response->assertStatus(403);
        $response->assertSee('Access denied. Admin privileges required.');
    }

    #[Test]
    public function regular_users_cannot_access_admin_users_edit()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($this->regularUser)
            ->get(route('admin.users.edit', $user));

        $response->assertStatus(403);
        $response->assertSee('Access denied. Admin privileges required.');
    }

    #[Test]
    public function non_admin_users_cannot_create_users_via_post()
    {
        $userData = [
            'name' => 'Unauthorized User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'user',
        ];

        $response = $this->actingAs($this->memberUser)
            ->post(route('admin.users.store'), $userData);

        $response->assertStatus(403);
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
    }

    #[Test]
    public function non_admin_users_cannot_update_users_via_put()
    {
        $user = User::factory()->create(['name' => 'Original Name']);

        $updateData = [
            'name' => 'Unauthorized Update',
            'email' => $user->email,
            'role' => $user->role,
            'account_status' => 'active',
        ];

        $response = $this->actingAs($this->memberUser)
            ->put(route('admin.users.update', $user), $updateData);

        $response->assertStatus(403);

        $user->refresh();
        $this->assertEquals('Original Name', $user->name);
    }

    #[Test]
    public function non_admin_users_cannot_delete_users_via_delete()
    {
        $userToDelete = User::factory()->create();

        $response = $this->actingAs($this->memberUser)
            ->delete(route('admin.users.destroy', $userToDelete));

        $response->assertStatus(403);
        $this->assertDatabaseHas('users', ['id' => $userToDelete->id]);
    }

    #[Test]
    public function non_admin_users_cannot_update_user_roles()
    {
        $user = User::factory()->create(['role' => 'user']);

        $response = $this->actingAs($this->memberUser)
            ->post(route('admin.users.update-role', $user), ['role' => 'admin']);

        $response->assertStatus(403);

        $user->refresh();
        $this->assertEquals('user', $user->role);
    }

    #[Test]
    public function non_admin_users_cannot_lock_user_accounts()
    {
        $user = User::factory()->create(['locked_until' => null]);

        $response = $this->actingAs($this->memberUser)
            ->post(route('admin.users.lock', $user));

        $response->assertStatus(403);

        $user->refresh();
        $this->assertFalse($user->isLocked());
    }

    #[Test]
    public function non_admin_users_cannot_unlock_user_accounts()
    {
        $user = User::factory()->create([
            'locked_until' => now()->addHours(24),
            'failed_login_attempts' => 5,
        ]);

        $response = $this->actingAs($this->memberUser)
            ->post(route('admin.users.unlock', $user));

        $response->assertStatus(403);

        $user->refresh();
        $this->assertTrue($user->isLocked());
    }

    #[Test]
    public function admin_middleware_protects_all_user_management_routes()
    {
        $user = User::factory()->create();
        $routes = [
            ['GET', route('admin.users.index')],
            ['GET', route('admin.users.create')],
            ['GET', route('admin.users.show', $user)],
            ['GET', route('admin.users.edit', $user)],
            ['POST', route('admin.users.store')],
            ['PUT', route('admin.users.update', $user)],
            ['DELETE', route('admin.users.destroy', $user)],
            ['POST', route('admin.users.update-role', $user)],
            ['POST', route('admin.users.lock', $user)],
            ['POST', route('admin.users.unlock', $user)],
        ];

        foreach ($routes as [$method, $route]) {
            $response = $this->actingAs($this->memberUser)
                ->call($method, $route);

            $this->assertEquals(403, $response->getStatusCode(),
                "Route {$method} {$route} should return 403 for non-admin users");
        }
    }

    #[Test]
    public function admin_has_access_to_all_user_management_routes()
    {
        $user = User::factory()->create();
        $routes = [
            ['GET', route('admin.users.index')],
            ['GET', route('admin.users.create')],
            ['GET', route('admin.users.show', $user)],
            ['GET', route('admin.users.edit', $user)],
        ];

        foreach ($routes as [$method, $route]) {
            $response = $this->actingAs($this->adminUser)
                ->call($method, $route);

            $this->assertEquals(200, $response->getStatusCode(),
                "Route {$method} {$route} should return 200 for admin users");
        }
    }
}
