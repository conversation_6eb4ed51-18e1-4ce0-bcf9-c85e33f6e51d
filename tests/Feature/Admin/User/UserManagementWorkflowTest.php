<?php

namespace Tests\Feature\Admin\User;

use App\Http\Controllers\Admin\UserManagementController;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(UserManagementController::class)]
class UserManagementWorkflowTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);
    }

    #[Test]
    public function complete_user_creation_workflow()
    {
        // Step 1: Admin accesses user creation page
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.create'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.users.create');

        // Step 2: Admin submits user creation form
        $userData = [
            'name' => 'Workflow Test User',
            'email' => '<EMAIL>',
            'username' => 'workflowuser',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'member',
        ];

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.store'), $userData);

        // Step 3: Verify user is created and redirected to show page
        $response->assertStatus(302);
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $response->assertRedirect(route('admin.users.show', $user));
        $response->assertSessionHas('success', 'User created successfully.');

        // Step 4: Admin views the newly created user
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.show', $user));

        $response->assertStatus(200);
        $response->assertViewIs('admin.users.show');
        $response->assertSee('Workflow Test User');
        $response->assertSee('<EMAIL>');
        $response->assertSee('workflowuser');
        $response->assertSee('member');
    }

    #[Test]
    public function complete_user_editing_workflow()
    {
        // Step 1: Create a user to edit
        $user = User::factory()->create([
            'name' => 'Original Name',
            'email' => '<EMAIL>',
            'role' => 'user',
        ]);

        // Step 2: Admin accesses user edit page
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.edit', $user));

        $response->assertStatus(200);
        $response->assertViewIs('admin.users.edit');
        $response->assertSee('Original Name');

        // Step 3: Admin submits user update form
        $updateData = [
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
            'username' => 'updateduser',
            'role' => 'member',
            'account_status' => 'active',
        ];

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user), $updateData);

        // Step 4: Verify user is updated and redirected to show page
        $response->assertStatus(302);
        $response->assertRedirect(route('admin.users.show', $user));
        $response->assertSessionHas('success', 'User updated successfully.');

        // Step 5: Verify changes are persisted
        $user->refresh();
        $this->assertEquals('Updated Name', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('updateduser', $user->username);
        $this->assertEquals('member', $user->role);

        // Step 6: Admin views the updated user
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.show', $user));

        $response->assertStatus(200);
        $response->assertSee('Updated Name');
        $response->assertSee('<EMAIL>');
        $response->assertSee('member');
    }

    #[Test]
    public function complete_user_role_management_workflow()
    {
        // Step 1: Create a user with initial role
        $user = User::factory()->create(['role' => 'user']);

        // Step 2: Admin views user details
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.show', $user));

        $response->assertStatus(200);
        $response->assertSee('user'); // Current role

        // Step 3: Admin updates user role
        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.update-role', $user), ['role' => 'member']);

        $response->assertStatus(302);
        $response->assertSessionHas('success', 'User role updated successfully to member.');

        // Step 4: Verify role change
        $user->refresh();
        $this->assertEquals('member', $user->role);

        // Step 5: Admin promotes to admin role
        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.update-role', $user), ['role' => 'admin']);

        $response->assertStatus(302);
        $response->assertSessionHas('success', 'User role updated successfully to admin.');

        // Step 6: Verify final role change
        $user->refresh();
        $this->assertEquals('admin', $user->role);
    }

    #[Test]
    public function complete_user_account_locking_workflow()
    {
        // Step 1: Create an active user
        $user = User::factory()->create([
            'role' => 'member',
            'locked_until' => null,
            'failed_login_attempts' => 0,
        ]);

        // Step 2: Verify user is initially unlocked
        $this->assertFalse($user->isLocked());

        // Step 3: Admin locks the user account
        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.lock', $user));

        $response->assertStatus(302);
        $response->assertSessionHas('success', "User '{$user->name}' has been locked for 24 hours.");

        // Step 4: Verify user is locked
        $user->refresh();
        $this->assertTrue($user->isLocked());
        $this->assertEquals(5, $user->failed_login_attempts);

        // Step 5: Admin unlocks the user account
        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.unlock', $user));

        $response->assertStatus(302);
        $response->assertSessionHas('success', "User '{$user->name}' has been unlocked successfully.");

        // Step 6: Verify user is unlocked
        $user->refresh();
        $this->assertFalse($user->isLocked());
        $this->assertEquals(0, $user->failed_login_attempts);
        $this->assertNull($user->locked_until);
    }

    #[Test]
    public function complete_user_deletion_workflow()
    {
        // Step 1: Create a user to delete with unique name
        $uniqueName = 'UniqueUserToDelete'.time();
        $userToDelete = User::factory()->create([
            'name' => $uniqueName,
            'email' => '<EMAIL>',
            'role' => 'member',
        ]);

        // Step 2: Admin views user in index
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.index'));

        $response->assertStatus(200);
        $response->assertSee($uniqueName);

        // Step 3: Admin views user details
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.show', $userToDelete));

        $response->assertStatus(200);
        $response->assertSee($uniqueName);

        // Step 4: Admin deletes the user
        $response = $this->actingAs($this->adminUser)
            ->delete(route('admin.users.destroy', $userToDelete));

        $response->assertStatus(302);
        $response->assertRedirect(route('admin.users.index'));
        $response->assertSessionHas('success', "User '{$uniqueName}' has been deleted successfully.");

        // Step 5: Verify user is deleted from database
        $this->assertDatabaseMissing('users', ['id' => $userToDelete->id]);
        $this->assertDatabaseMissing('users', ['name' => $uniqueName]);
    }

    #[Test]
    public function complete_user_password_reset_workflow()
    {
        // Step 1: Create a user
        $user = User::factory()->create([
            'name' => 'Password Reset User',
            'email' => '<EMAIL>',
            'role' => 'member',
        ]);
        $originalPasswordHash = $user->password;

        // Step 2: Admin accesses user edit page
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.edit', $user));

        $response->assertStatus(200);
        $response->assertViewIs('admin.users.edit');

        // Step 3: Admin updates user password
        $updateData = [
            'name' => $user->name,
            'email' => $user->email,
            'username' => $user->username,
            'role' => $user->role,
            'account_status' => 'active',
            'password' => 'newpassword123',
            'password_confirmation' => 'newpassword123',
        ];

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user), $updateData);

        // Step 4: Verify password is updated
        $response->assertStatus(302);
        $response->assertRedirect(route('admin.users.show', $user));
        $response->assertSessionHas('success', 'User updated successfully.');

        $user->refresh();
        $this->assertNotEquals($originalPasswordHash, $user->password);
        $this->assertTrue(Hash::check('newpassword123', $user->password));
    }

    #[Test]
    public function complete_failed_login_attempts_management_workflow()
    {
        // Step 1: Create a user with failed login attempts
        $user = User::factory()->create([
            'role' => 'member',
            'failed_login_attempts' => 3,
            'locked_until' => now()->addMinutes(30),
        ]);

        // Step 2: Verify user has failed attempts and is locked
        $this->assertEquals(3, $user->failed_login_attempts);
        $this->assertTrue($user->isLocked());

        // Step 3: Admin accesses user edit page
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.edit', $user));

        $response->assertStatus(200);

        // Step 4: Admin resets failed login attempts
        $updateData = [
            'name' => $user->name,
            'email' => $user->email,
            'username' => $user->username,
            'role' => $user->role,
            'account_status' => 'active',
            'reset_failed_attempts' => '1',
        ];

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user), $updateData);

        // Step 5: Verify failed attempts are reset and user is unlocked
        $response->assertStatus(302);
        $response->assertSessionHas('success', 'User updated successfully.');

        $user->refresh();
        $this->assertEquals(0, $user->failed_login_attempts);
        $this->assertFalse($user->isLocked());
        $this->assertNull($user->locked_until);
    }

    #[Test]
    public function admin_user_management_navigation_workflow()
    {
        // Create some test users
        $users = User::factory()->count(5)->create();

        // Step 1: Admin accesses users index
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.users.index');

        // Step 2: Admin navigates to create new user
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.create'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.users.create');

        // Step 3: Admin navigates to view specific user
        $user = $users->first();
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.show', $user));

        $response->assertStatus(200);
        $response->assertViewIs('admin.users.show');

        // Step 4: Admin navigates to edit user
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.edit', $user));

        $response->assertStatus(200);
        $response->assertViewIs('admin.users.edit');

        // Step 5: Admin navigates back to users index
        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.users.index');
    }
}
