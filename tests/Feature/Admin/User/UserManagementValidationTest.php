<?php

namespace Tests\Feature\Admin\User;

use App\Http\Controllers\Admin\UserManagementController;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(UserManagementController::class)]
class UserManagementValidationTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);
    }

    #[Test]
    public function store_validates_required_fields()
    {
        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.store'), []);

        $response->assertStatus(302);
        $response->assertSessionHasErrors(['name', 'email', 'password', 'role']);
        $response->assertRedirect();
    }

    #[Test]
    public function store_validates_email_format()
    {
        $userData = [
            'name' => 'Test User',
            'email' => 'invalid-email',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'user',
        ];

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.store'), $userData);

        $response->assertStatus(302);
        $response->assertSessionHasErrors(['email']);
        $this->assertDatabaseMissing('users', ['email' => 'invalid-email']);
    }

    #[Test]
    public function store_validates_unique_email()
    {
        $existingUser = User::factory()->create(['email' => '<EMAIL>']);

        $userData = [
            'name' => 'Duplicate Email User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'user',
        ];

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.store'), $userData);

        $response->assertStatus(302);
        $response->assertSessionHasErrors(['email']);
        $this->assertEquals(1, User::where('email', '<EMAIL>')->count());
    }

    #[Test]
    public function store_validates_unique_username()
    {
        $existingUser = User::factory()->create(['username' => 'existinguser']);

        $userData = [
            'name' => 'Duplicate Username User',
            'email' => '<EMAIL>',
            'username' => 'existinguser',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'user',
        ];

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.store'), $userData);

        $response->assertStatus(302);
        $response->assertSessionHasErrors(['username']);
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
    }

    #[Test]
    public function store_validates_password_confirmation()
    {
        $userData = [
            'name' => 'Password Mismatch User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'differentpassword',
            'role' => 'user',
        ];

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.store'), $userData);

        $response->assertStatus(302);
        $response->assertSessionHasErrors(['password']);
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
    }

    #[Test]
    public function store_validates_password_minimum_length()
    {
        $userData = [
            'name' => 'Short Password User',
            'email' => '<EMAIL>',
            'password' => '1234567', // Only 7 characters
            'password_confirmation' => '1234567',
            'role' => 'user',
        ];

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.store'), $userData);

        $response->assertStatus(302);
        $response->assertSessionHasErrors(['password']);
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
    }

    #[Test]
    public function store_validates_role_values()
    {
        $userData = [
            'name' => 'Invalid Role User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'invalid_role',
        ];

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.store'), $userData);

        $response->assertStatus(302);
        $response->assertSessionHasErrors(['role']);
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
    }

    #[Test]
    public function store_validates_username_alpha_dash()
    {
        $userData = [
            'name' => 'Invalid Username User',
            'email' => '<EMAIL>',
            'username' => 'invalid username!', // Contains spaces and special chars
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'user',
        ];

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.store'), $userData);

        $response->assertStatus(302);
        $response->assertSessionHasErrors(['username']);
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
    }

    #[Test]
    public function store_validates_name_max_length()
    {
        $userData = [
            'name' => str_repeat('a', 256), // 256 characters, exceeds max of 255
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'user',
        ];

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.store'), $userData);

        $response->assertStatus(302);
        $response->assertSessionHasErrors(['name']);
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
    }

    #[Test]
    public function update_validates_required_fields()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user), []);

        $response->assertStatus(302);
        $response->assertSessionHasErrors(['name', 'email', 'role', 'account_status']);
        $response->assertRedirect();
    }

    #[Test]
    public function update_validates_unique_email_ignoring_current_user()
    {
        $user1 = User::factory()->create(['email' => '<EMAIL>']);
        $user2 = User::factory()->create(['email' => '<EMAIL>']);

        // Try to update user1 with user2's email
        $updateData = [
            'name' => $user1->name,
            'email' => '<EMAIL>',
            'username' => $user1->username,
            'role' => $user1->role,
            'account_status' => 'active',
        ];

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user1), $updateData);

        $response->assertStatus(302);
        $response->assertSessionHasErrors(['email']);

        $user1->refresh();
        $this->assertEquals('<EMAIL>', $user1->email);
    }

    #[Test]
    public function update_allows_keeping_same_email()
    {
        $user = User::factory()->create([
            'name' => 'Original Name',
            'email' => '<EMAIL>',
            'role' => 'member',
        ]);

        $updateData = [
            'name' => 'Updated Name',
            'email' => '<EMAIL>', // Same email
            'username' => $user->username,
            'role' => $user->role,
            'account_status' => 'active',
        ];

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user), $updateData);

        $response->assertStatus(302);
        $response->assertSessionDoesntHaveErrors(['email']);

        $user->refresh();
        $this->assertEquals('Updated Name', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
    }

    #[Test]
    public function update_validates_password_confirmation_when_password_provided()
    {
        $user = User::factory()->create();

        $updateData = [
            'name' => $user->name,
            'email' => $user->email,
            'username' => $user->username,
            'role' => $user->role,
            'account_status' => 'active',
            'password' => 'newpassword123',
            'password_confirmation' => 'differentpassword',
        ];

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user), $updateData);

        $response->assertStatus(302);
        $response->assertSessionHasErrors(['password']);
    }

    #[Test]
    public function update_validates_account_status_values()
    {
        $user = User::factory()->create();

        $updateData = [
            'name' => $user->name,
            'email' => $user->email,
            'username' => $user->username,
            'role' => $user->role,
            'account_status' => 'invalid_status',
        ];

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user), $updateData);

        $response->assertStatus(302);
        $response->assertSessionHasErrors(['account_status']);
    }

    #[Test]
    public function update_role_validates_role_values()
    {
        $user = User::factory()->create(['role' => 'user']);

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.update-role', $user), ['role' => 'invalid_role']);

        $response->assertStatus(302);
        $response->assertSessionHasErrors(['role']);

        $user->refresh();
        $this->assertEquals('user', $user->role);
    }
}
