<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Admin User
        User::create([
            'name' => 'Admin User',
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        // Create Member User
        User::create([
            'name' => 'Member User',
            'username' => 'member',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'member',
            'email_verified_at' => now(),
        ]);

        // Create Regular User
        User::create([
            'name' => 'Regular User',
            'username' => 'user',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'user',
            'email_verified_at' => now(),
        ]);

        // Create FPMP Admin User
        User::create([
            'name' => '<PERSON>',
            'username' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        // Create FPMP Admin User
        User::create([
            'name' => 'Marc Janga',
            'username' => 'Marc',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        // Create additional FPMP users
        User::create([
            'name' => 'Hensly Klaber',
            'username' => 'Chino',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'member',
            'email_verified_at' => now(),
        ]);

        User::create([
            'name' => 'Airich Van Aanholt',
            'username' => 'Aintje',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'member',
            'email_verified_at' => now(),
        ]);

        User::create([
            'name' => 'Michael Doran',
            'username' => 'Michael',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'member',
            'email_verified_at' => now(),
        ]);

        User::create([
            'name' => 'Sirvin Maduro',
            'username' => 'Vincho',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'member',
            'email_verified_at' => now(),
        ]);

        User::create([
            'name' => 'Claydy Thomas',
            'username' => 'Thomas',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'member',
            'email_verified_at' => now(),
        ]);

        User::create([
            'name' => 'Richinel Bernabela',
            'username' => 'Richinel',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'member',
            'email_verified_at' => now(),
        ]);
    }
}
