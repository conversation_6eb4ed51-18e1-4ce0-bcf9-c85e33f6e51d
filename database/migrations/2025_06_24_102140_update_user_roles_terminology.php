<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $driver = DB::getDriverName();

        if ($driver === 'sqlite') {
            // For SQLite, we need to recreate the table with new enum values
            Schema::create('users_temp', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('email')->unique();
                $table->string('username')->unique()->nullable();
                $table->enum('role', ['admin', 'member', 'user'])->default('member');
                $table->timestamp('email_verified_at')->nullable();
                $table->string('password');
                $table->integer('failed_login_attempts')->default(0);
                $table->timestamp('locked_until')->nullable();
                $table->rememberToken();
                $table->timestamps();

                $table->index(['role']);
                $table->index(['username']);
                $table->index(['failed_login_attempts']);
            });

            // Copy data with role mapping
            DB::statement("
                INSERT INTO users_temp (id, name, email, username, role, email_verified_at, password, failed_login_attempts, locked_until, remember_token, created_at, updated_at)
                SELECT
                    id,
                    name,
                    email,
                    username,
                    CASE
                        WHEN role = 'normal_user' THEN 'member'
                        WHEN role = 'client' THEN 'user'
                        ELSE role
                    END as role,
                    email_verified_at,
                    password,
                    failed_login_attempts,
                    locked_until,
                    remember_token,
                    created_at,
                    updated_at
                FROM users
            ");

            // Drop old table and rename temp table
            Schema::drop('users');
            Schema::rename('users_temp', 'users');
        } elseif ($driver === 'pgsql') {
            // For PostgreSQL, we need to handle ENUM modifications differently

            // First, update the existing data
            DB::table('users')->where('role', 'normal_user')->update(['role' => 'member']);
            DB::table('users')->where('role', 'client')->update(['role' => 'user']);

            // Create new ENUM type with updated values
            DB::statement("CREATE TYPE user_role_new AS ENUM ('admin', 'member', 'user')");

            // Add a temporary column with the new type
            DB::statement("ALTER TABLE users ADD COLUMN role_new user_role_new DEFAULT 'member'");

            // Copy data from old column to new column
            DB::statement('UPDATE users SET role_new = role::text::user_role_new');

            // Drop the old column and its default
            DB::statement('ALTER TABLE users DROP COLUMN role');

            // Rename the new column to the original name
            DB::statement('ALTER TABLE users RENAME COLUMN role_new TO role');

            // Set NOT NULL constraint
            DB::statement('ALTER TABLE users ALTER COLUMN role SET NOT NULL');

            // Drop the old ENUM type (it might be named differently, so we'll try to drop it safely)
            DB::statement('DROP TYPE IF EXISTS user_role_old CASCADE');

            // Rename the new type to the standard name for future use
            DB::statement('ALTER TYPE user_role_new RENAME TO user_role_old');
        } else {
            // For MySQL
            DB::table('users')->where('role', 'normal_user')->update(['role' => 'member']);
            DB::table('users')->where('role', 'client')->update(['role' => 'user']);
            DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'member', 'user') NOT NULL DEFAULT 'member'");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $driver = DB::getDriverName();

        if ($driver === 'sqlite') {
            // For SQLite, recreate table with old enum values
            Schema::create('users_temp', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('email')->unique();
                $table->string('username')->unique()->nullable();
                $table->enum('role', ['admin', 'normal_user', 'client'])->default('normal_user');
                $table->timestamp('email_verified_at')->nullable();
                $table->string('password');
                $table->integer('failed_login_attempts')->default(0);
                $table->timestamp('locked_until')->nullable();
                $table->rememberToken();
                $table->timestamps();

                $table->index(['role']);
                $table->index(['username']);
                $table->index(['failed_login_attempts']);
            });

            // Copy data with role mapping back
            DB::statement("
                INSERT INTO users_temp (id, name, email, username, role, email_verified_at, password, failed_login_attempts, locked_until, remember_token, created_at, updated_at)
                SELECT
                    id,
                    name,
                    email,
                    username,
                    CASE
                        WHEN role = 'member' THEN 'normal_user'
                        WHEN role = 'user' THEN 'client'
                        ELSE role
                    END as role,
                    email_verified_at,
                    password,
                    failed_login_attempts,
                    locked_until,
                    remember_token,
                    created_at,
                    updated_at
                FROM users
            ");

            Schema::drop('users');
            Schema::rename('users_temp', 'users');
        } elseif ($driver === 'pgsql') {
            // For PostgreSQL, reverse the ENUM modification

            // First, update the data back to original values
            DB::table('users')->where('role', 'member')->update(['role' => 'normal_user']);
            DB::table('users')->where('role', 'user')->update(['role' => 'client']);

            // Create the original ENUM type
            DB::statement("CREATE TYPE user_role_original AS ENUM ('admin', 'normal_user', 'client')");

            // Add a temporary column with the original type
            DB::statement("ALTER TABLE users ADD COLUMN role_original user_role_original DEFAULT 'normal_user'");

            // Copy data from current column to new column
            DB::statement('UPDATE users SET role_original = role::text::user_role_original');

            // Drop the current column
            DB::statement('ALTER TABLE users DROP COLUMN role');

            // Rename the new column to the original name
            DB::statement('ALTER TABLE users RENAME COLUMN role_original TO role');

            // Set NOT NULL constraint
            DB::statement('ALTER TABLE users ALTER COLUMN role SET NOT NULL');

            // Clean up the type we created in the up() method
            DB::statement('DROP TYPE IF EXISTS user_role_old CASCADE');

            // Rename the type for consistency
            DB::statement('ALTER TYPE user_role_original RENAME TO user_role_old');
        } else {
            // For MySQL
            DB::table('users')->where('role', 'member')->update(['role' => 'normal_user']);
            DB::table('users')->where('role', 'user')->update(['role' => 'client']);
            DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'normal_user', 'client') NOT NULL DEFAULT 'normal_user'");
        }
    }
};
