# Laravel RBAC System Documentation

## Overview
This Laravel application implements a comprehensive Role-Based Access Control (RBAC) system with three distinct user roles and enhanced authentication features.

## User Roles and Access Levels

### 1. Admin Role (`admin`)
- **Full system access** including user management
- Can view and manage all users, fields, utilities, and reservations
- Can assign/change user roles
- Access to admin dashboard and all features
- **Routes accessible:**
  - `/admin/dashboard` - Admin dashboard
  - `/admin/users` - User management interface
  - `/admin/users/{user}` - Individual user details
  - `/admin/fields` - Field management
  - `/admin/utilities` - Utility management
  - All member and user routes

### 2. Member Role (`member`)
- **Extended application features** access
- Cannot access admin functions
- Can access member features and all user features
- **Routes accessible:**
  - `/member/dashboard` - Member dashboard
  - `/member/features` - Member features
  - All user routes including reservations

### 3. User Role (`user`)
- **Basic access** to reservations and personal profile
- Most restrictive role level
- **Routes accessible:**
  - `/user/dashboard` - User dashboard
  - `/profile` - Personal profile management
  - `/reservations` - Reservation management (create, view, cancel)

## Authentication Features

### Enhanced Login System
- **Dual login support**: Users can login with either email address or username
- **Account lockout**: After 5 failed login attempts, accounts are locked for 30 minutes
- **Comprehensive error handling**: User-friendly error messages for various scenarios
- **Session management**: Proper session destruction on logout

### Security Features
- **CSRF protection** on all forms
- **Input validation** using Laravel's built-in validation
- **Password hashing** using Laravel's secure hashing
- **Rate limiting** to prevent brute force attacks
- **Middleware protection** for role-based route access

## Database Schema

### Users Table Enhancements
```sql
- id (primary key)
- name (string)
- email (string, unique)
- username (string, unique, nullable)
- password (hashed)
- role (enum: 'admin', 'member', 'user', default: 'member')
- failed_login_attempts (integer, default: 0)
- locked_until (timestamp, nullable)
- email_verified_at (timestamp, nullable)
- remember_token
- created_at, updated_at
```

### Indexes
- `role` - For efficient role-based queries
- `username` - For username login performance
- `failed_login_attempts` - For lockout functionality

## Middleware System

### Custom Middleware Classes
1. **AdminMiddleware** - Restricts access to admin-only routes
2. **MemberMiddleware** - Allows member and admin access
3. **UserMiddleware** - Allows user, member, and admin access

### Middleware Registration
Middleware are registered in `bootstrap/app.php` with aliases:
- `admin` → AdminMiddleware
- `member` → MemberMiddleware
- `user` → UserMiddleware
- `normal_user` → MemberMiddleware (legacy compatibility)
- `client` → UserMiddleware (legacy compatibility)

## Test Users (Seeded Data)

The system includes pre-seeded test users for each role:

### Admin Users
- **Email:** <EMAIL> | **Username:** admin | **Password:** password
- **Email:** <EMAIL> | **Username:** Andy | **Password:** password
- **Email:** <EMAIL> | **Username:** Marc | **Password:** password

### Member Users
- **Email:** <EMAIL> | **Username:** member | **Password:** password
- **Email:** <EMAIL> | **Username:** Chino | **Password:** password
- **Email:** <EMAIL> | **Username:** Aintje | **Password:** password
- **Email:** <EMAIL> | **Username:** Michael | **Password:** password
- **Email:** <EMAIL> | **Username:** Vincho | **Password:** password
- **Email:** <EMAIL> | **Username:** Thomas | **Password:** password
- **Email:** <EMAIL> | **Username:** Richinel | **Password:** password

### Regular Users
- **Email:** <EMAIL> | **Username:** user | **Password:** password

## User Management Interface

### Admin Features
- **User listing** with pagination
- **Role assignment** via dropdown selection
- **User status monitoring** (active/locked)
- **Failed login attempt tracking**
- **Individual user detail views**

### Role Assignment
- Admins can change user roles through the user management interface
- Protection against self-demotion (admins cannot remove their own admin privileges)
- Real-time role updates with success/error messaging

## Navigation System

### Role-Based Navigation
- **Dynamic menu items** based on user role
- **Admin users** see "User Management" link
- **Member users and admins** see "Features" link
- **All users** see appropriate dashboard links

### User Information Display
- User's current role displayed in navigation
- Role-specific dashboard redirection
- Contextual access information

## Technical Implementation

### PSR-12 Compliance
- All code follows PSR-12 coding standards
- Proper namespacing and class organization
- Consistent formatting and documentation

### Laravel Best Practices
- **Eloquent ORM** for all database operations
- **Form Request validation** for authentication
- **Blade templating** with component reuse
- **Route model binding** for user management
- **Middleware groups** for route protection

### Error Handling
- **Graceful error messages** for authentication failures
- **Account lockout notifications** with clear instructions
- **403 Forbidden responses** for unauthorized access
- **Validation error display** in forms

## Testing

### Automated Tests
The system includes comprehensive tests covering:
- **Email and username login** functionality
- **Role-based middleware protection**
- **Account lockout mechanism**
- **Dashboard redirection** based on roles
- **Admin access verification**

### Manual Testing Checklist
1. ✅ Login with email and username
2. ✅ Role-based access control
3. ✅ Account lockout after failed attempts
4. ✅ User role assignment by admins
5. ✅ Dashboard redirection by role
6. ✅ Navigation menu role visibility

## Deployment Notes

### Requirements
- PHP 8.2+
- Laravel 12.0+
- SQLite/MySQL database
- Composer for dependency management

### Setup Instructions
1. Clone the repository
2. Run `composer install`
3. Configure `.env` file for database
4. Run `php artisan migrate`
5. Run `php artisan db:seed` to create test users
6. Start server with `php artisan serve`

### Security Considerations
- Change default test user passwords in production
- Configure proper session and cache drivers
- Set up SSL/TLS for production deployment
- Review and adjust rate limiting settings
- Configure proper email verification if required

## Support and Maintenance

### User Role Management
- Admins can promote/demote users through the web interface
- Database seeders can be re-run to reset test data
- User accounts can be manually unlocked by updating `locked_until` to null

### System Monitoring
- Failed login attempts are tracked per user
- Account lockouts are automatically released after 30 minutes
- Admin dashboard provides user statistics and overview

This RBAC system provides a solid foundation for role-based access control in Laravel applications with comprehensive security features and user management capabilities.
